# 第二章：标准电路模板库

## 📚 学习目标
- 掌握积分器、微分器、求和器的设计方法
- 学会振荡器电路的参数计算
- 能够快速选择和应用标准电路模板
- 理解各种电路的适用场景和限制

---

## 2.1 反相积分器

### 2.1.1 基本电路

**电路图：**
```
Vin ----[R]----+----[-]
                |       \
                |        △----→ Vout
               [C]      /
                |      [+]
               GND      |
                       GND
```

**元件标注：**
- R：积分电阻
- C：积分电容
- 运放：LM324中的任意一个

### 2.1.2 传递函数推导

**步骤1：应用虚短虚断**
- V+ = 0V（接地）
- V- = V+ = 0V
- I+ = I- = 0

**步骤2：电流分析**
```
流过R的电流：IR = (Vin - V-)/R = Vin/R
流过C的电流：IC = C × d(V- - Vout)/dt = -C × dVout/dt
```

**步骤3：根据虚断条件**
```
IR = IC
Vin/R = -C × dVout/dt
dVout/dt = -Vin/(RC)
```

**步骤4：积分得到传递函数**
```
Vout = -1/(RC) ∫ Vin dt + Vout(0)
```

**传递函数：**
```
H(s) = Vout(s)/Vin(s) = -1/(RCs)
```

### 2.1.3 标准参数配置

**配置A：通用积分器**
- R = 10kΩ
- C = 0.1μF  
- 时间常数：τ = RC = 1ms
- 积分常数：K = 1/(RC) = 1000 s⁻¹

**配置B：慢速积分器**
- R = 100kΩ
- C = 1μF
- 时间常数：τ = RC = 100ms  
- 积分常数：K = 1/(RC) = 10 s⁻¹

**配置C：快速积分器**
- R = 1kΩ
- C = 0.01μF
- 时间常数：τ = RC = 0.01ms
- 积分常数：K = 1/(RC) = 100,000 s⁻¹

### 2.1.4 数值计算示例

**示例1：方波输入**
```
给定：R = 10kΩ, C = 0.1μF, Vin = 1V方波
求解：输出波形

解：积分常数 K = 1/(RC) = 1/(10×10³ × 0.1×10⁻⁶) = 1000 s⁻¹
    对于1V方波输入，输出为三角波
    斜率 = -Vin/(RC) = -1V/1ms = -1000 V/s
```

**示例2：正弦波输入**
```
给定：R = 10kΩ, C = 0.1μF, Vin = sin(ωt)
求解：Vout = ?

解：Vout = -1/(RC) ∫ sin(ωt) dt
        = -1/(RC) × (-cos(ωt)/ω)
        = cos(ωt)/(RCω)
        = cos(ωt)/(1000ω)
```

### 2.1.5 初始条件设置

**方法1：电容预充电**
```
    +V0
     |
    [S1]  ← 开关，t=0时断开
     |
Vin-[R]-+----[-]
         |       \
        [C]       △----→ Vout
         |       /
        GND     [+]
                 |
                GND
```

**方法2：复位开关**
```
Vin-[R]-+----[-]
         |       \
        [C]       △----→ Vout
         |       /
        [S2]    [+]  ← S2为复位开关
         |       |
        GND     GND
```

### 2.1.6 应用场景

**适用场合：**
- 波形变换（方波→三角波）
- 模拟计算机中的积分运算
- 低通滤波器
- 信号平滑处理

**注意事项：**
- 需要设置初始条件
- 输入信号不能有直流分量
- 长时间积分会导致输出饱和
- 需要定期复位

---

## 2.2 微分器电路

### 2.2.1 基本微分器

**电路图：**
```
Vin ----[C]----+----[-]
                |       \
                |        △----→ Vout
               [R]      /
                |      [+]
               GND      |
                       GND
```

**传递函数：**
```
H(s) = Vout(s)/Vin(s) = -RCs
Vout = -RC × dVin/dt
```

**标准参数：**
- C = 0.01μF
- R = 10kΩ  
- 微分常数：K = RC = 0.1ms

### 2.2.2 改进型微分器（推荐）

**问题：** 基本微分器在高频时增益无限大，容易放大噪声

**解决方案：** 添加限频电阻

**改进电路：**
```
Vin ----[C]----[R1]----+----[-]
                        |       \
                        |        △----→ Vout
                       [R2]     /
                        |      [+]
                       GND      |
                              GND
```

**参数设计：**
- C = 0.01μF
- R1 = 1kΩ（限频电阻）
- R2 = 10kΩ（反馈电阻）
- 截止频率：fc = 1/(2πR1C) ≈ 16kHz

**传递函数：**
```
H(s) = -R2Cs/(1 + R1Cs)
```

### 2.2.3 数值计算示例

**示例：三角波输入**
```
给定：R = 10kΩ, C = 0.01μF, 输入为1V、1kHz三角波
求解：输出波形

解：微分常数 K = RC = 10×10³ × 0.01×10⁻⁶ = 0.1ms
    三角波的斜率为常数，微分后得到方波
    输出幅度 = K × 斜率 = 0.1ms × (2V/0.5ms) = 0.4V
```

### 2.2.4 应用场景

**适用场合：**
- 边沿检测
- 波形变换（三角波→方波）
- 高通滤波器
- 信号微分运算

**使用建议：**
- 优先使用改进型微分器
- 输入信号要平滑，避免突变
- 注意高频噪声问题
- 工作频率不宜过高

---

## 2.3 求和器电路

### 2.3.1 反相求和器

**电路图：**
```
V1 ----[R1]----+
                |
V2 ----[R2]----+----[-]
                |       \
V3 ----[R3]----+        △----→ Vout
                |       /
               [Rf]    [+]
                |       |
               Vout    GND
```

**传递函数：**
```
Vout = -(Rf/R1×V1 + Rf/R2×V2 + Rf/R3×V3)
```

### 2.3.2 标准配置表

**配置A：等权重求和（增益=1）**
- R1 = R2 = R3 = Rf = 10kΩ
- Vout = -(V1 + V2 + V3)

**配置B：加权求和**
- R1 = 10kΩ, R2 = 20kΩ, R3 = 50kΩ, Rf = 10kΩ
- Vout = -(V1 + 0.5×V2 + 0.2×V3)

**配置C：比例求和**
- R1 = R2 = R3 = 30kΩ, Rf = 10kΩ  
- Vout = -0.33×(V1 + V2 + V3)

### 2.3.3 同相求和器

**电路图：**
```
V1 ----[R1]----+
                |
V2 ----[R2]----+----[+]
                |       \
               [R0]      △----→ Vout
                |       /
               GND     [-]
                        |
                       [Ra]
                        |
                       [Rb]----Vout
                        |
                       GND
```

**传递函数：**
```
Vout = (1 + Ra/Rb) × (V1/R1 + V2/R2)/(1/R1 + 1/R2 + 1/R0)
```

**标准参数：**
- R1 = R2 = R0 = 30kΩ
- Ra = Rb = 10kΩ
- Vout = (V1 + V2)/3 × 2 = 2(V1 + V2)/3

---

## 2.4 文氏桥振荡器

### 2.4.1 基本电路

**电路图：**
```
        +Vcc
          |
    +-----+-----+
    |           |
   [R3]        [R4]  ← 增益控制
    |           |
    +-----[-]   |
          |  \  |
          |   △-+----→ Vout
          |  /
    +----[+]
    |
    +----[R1]----+----[C1]----+
    |            |            |
   [C2]         Vout         GND
    |            |
   GND          [R2]
                 |
                GND
```

**振荡条件：**
1. **幅度条件：** Av = 1 + R4/R3 = 3
2. **相位条件：** 反馈网络相移为0°

### 2.4.2 频率计算

**振荡频率公式：**
```
f0 = 1/(2π√(R1R2C1C2))
```

**对于R1=R2=R, C1=C2=C的情况：**
```
f0 = 1/(2πRC)
```

### 2.4.3 标准参数配置

**配置A：音频振荡器（1kHz）**
- R1 = R2 = 1.6kΩ
- C1 = C2 = 0.1μF
- R3 = 10kΩ, R4 = 20kΩ
- f0 = 1/(2π × 1.6×10³ × 0.1×10⁻⁶) ≈ 1kHz

**配置B：竞赛专用（95.5Hz）**
- R1 = R2 = 16.7kΩ（用18kΩ标准值）
- C1 = C2 = 0.1μF
- R3 = 10kΩ, R4 = 20kΩ
- f0 = 1/(2π × 18×10³ × 0.1×10⁻⁶) ≈ 88.4Hz

**配置C：高频振荡器（10kHz）**
- R1 = R2 = 1.6kΩ
- C1 = C2 = 0.01μF
- R3 = 10kΩ, R4 = 20kΩ
- f0 = 1/(2π × 1.6×10³ × 0.01×10⁻⁶) ≈ 10kHz

### 2.4.4 幅度稳定电路

**问题：** 基本文氏桥振荡器幅度不稳定

**解决方案：** 使用二极管限幅

**改进电路：**
```
        +Vcc
          |
    +-----+-----+
    |           |
   [R3]    [D1] |  ← 稳幅二极管
    |       |   |
    +------[D2]-+
    |           |
    +-----[-]   |
          |  \  |
          |   △-+----→ Vout
          |  /
    +----[+]
    |
    (RC反馈网络)
```

**稳幅参数：**
- D1, D2：1N4148硅二极管
- 输出幅度稳定在约±0.7V

### 2.4.5 数值计算示例

**示例：设计600rad/s振荡器**
```
给定：ω = 600 rad/s
求解：R, C参数

解：f = ω/(2π) = 600/(2π) ≈ 95.5Hz
    选择C = 0.1μF
    R = 1/(2πfC) = 1/(2π × 95.5 × 0.1×10⁻⁶) ≈ 16.7kΩ
    选用标准值：R = 18kΩ
    实际频率：f = 1/(2π × 18×10³ × 0.1×10⁻⁶) ≈ 88.4Hz
```

---

## 2.5 三角波发生器

### 2.5.1 积分器级联法

**原理：** 方波 → 积分器 → 三角波

**电路连接：**
```
文氏桥振荡器 → 比较器 → 积分器 → 三角波输出
    (正弦波)     (方波)    (三角波)
```

**比较器电路：**
```
正弦波输入 ----[+]
               | \
               |  △----→ 方波输出
               | /
              [-]
               |
              GND
```

**积分器参数：**
- R = 10kΩ
- C = 0.1μF
- 输出三角波与输入方波同频

### 2.5.2 专用三角波发生器

**电路图：**
```
    +----[R1]----[-]
    |            | \
    |            |  △1----+----→ 方波输出
    |            | /      |
    +----[R2]---[+]       |
                          |
                         [R3]
                          |
    +----[R4]----[-]      |
    |            | \      |
    |            |  △2----+----→ 三角波输出
    |            | /
    +----[C]----[+]
```

**参数设计：**
- R1 = R2 = 10kΩ（比较器）
- R3 = 10kΩ（耦合电阻）
- R4 = 10kΩ, C = 0.1μF（积分器）
- 频率：f ≈ 1/(4R4C) ≈ 250Hz

---

## 2.6 快速选择表

### 2.6.1 电路功能对照表

| 输入信号 | 输出信号 | 推荐电路 | 标准参数 |
|---------|---------|---------|---------|
| 方波 | 三角波 | 积分器 | R=10kΩ, C=0.1μF |
| 三角波 | 方波 | 微分器 | R=10kΩ, C=0.01μF |
| 直流 | 正弦波 | 文氏桥 | R=18kΩ, C=0.1μF |
| 多路信号 | 求和 | 求和器 | R1=R2=Rf=10kΩ |
| 需要缓冲 | 隔离 | 跟随器 | 无需外接元件 |

### 2.6.2 频率范围对照表

| 目标频率 | R值(kΩ) | C值(μF) | 电路类型 |
|---------|---------|---------|---------|
| 1Hz | 160 | 1.0 | 文氏桥 |
| 10Hz | 16 | 1.0 | 文氏桥 |
| 100Hz | 16 | 0.1 | 文氏桥 |
| 1kHz | 1.6 | 0.1 | 文氏桥 |
| 10kHz | 1.6 | 0.01 | 文氏桥 |

### 2.6.3 积分时间常数表

| 时间常数τ | R值 | C值 | 应用场合 |
|----------|-----|-----|---------|
| 0.1ms | 1kΩ | 0.1μF | 快速积分 |
| 1ms | 10kΩ | 0.1μF | 通用积分 |
| 10ms | 100kΩ | 0.1μF | 慢速积分 |
| 100ms | 100kΩ | 1μF | 超慢积分 |

### 2.6.4 增益配置表

| 目标增益 | 反相放大器 | 同相放大器 | 应用说明 |
|---------|-----------|-----------|---------|
| 1 | R1=R2=10kΩ | 跟随器 | 单位增益 |
| 2 | R1=10kΩ,R2=20kΩ | R1=10kΩ,R2=10kΩ | 2倍放大 |
| 5 | R1=10kΩ,R2=50kΩ | R1=10kΩ,R2=40kΩ | 5倍放大 |
| 10 | R1=10kΩ,R2=100kΩ | R1=10kΩ,R2=90kΩ | 10倍放大 |

---

## 2.7 电路选择指南

### 2.7.1 根据功能选择

**信号变换类：**
- 方波→三角波：选择积分器
- 三角波→方波：选择微分器
- 直流→交流：选择振荡器
- 多信号合成：选择求和器

**信号处理类：**
- 信号放大：选择放大器
- 阻抗变换：选择跟随器
- 信号隔离：选择跟随器
- 相位调整：选择反相器

### 2.7.2 根据频率选择

**低频应用（<100Hz）：**
- 大电阻大电容组合
- 注意漂移问题
- 可能需要温度补偿

**中频应用（100Hz~10kHz）：**
- 标准参数配置
- 性能最佳频段
- 推荐工作范围

**高频应用（>10kHz）：**
- 小电阻小电容组合
- 注意带宽限制
- 可能需要高速运放

### 2.7.3 根据精度选择

**高精度要求：**
- 使用精密电阻（1%误差）
- 使用高质量电容
- 注意温度系数

**一般精度要求：**
- 标准电阻（5%误差）
- 普通电容
- 标准参数配置

---

## 📖 本章小结

**核心电路模板：**
1. **积分器：** Vout = -1/(RC)∫Vin dt，标准参数R=10kΩ, C=0.1μF
2. **微分器：** Vout = -RC dVin/dt，推荐改进型避免噪声
3. **求和器：** 多输入加权求和，标准配置等权重
4. **振荡器：** 文氏桥产生正弦波，f=1/(2πRC)

**快速选择原则：**
1. **功能优先：** 根据输入输出信号类型选择电路
2. **频率匹配：** 根据工作频率选择RC参数
3. **精度权衡：** 根据精度要求选择元件等级
4. **标准优先：** 优先使用标准参数配置

**下一章预告：**
第三章将提供详细的参数计算速查表，包含常用RC组合、频率对应关系等实用工具表格。
