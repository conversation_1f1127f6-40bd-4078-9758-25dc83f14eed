# 第六章：快速索引和检查清单

## 📚 使用说明
- 本章是整个教学文档的"导航中心"
- 按功能和问题类型快速定位相关内容
- 所有检查清单都经过实战验证
- 适合考场紧张环境下快速使用

---

## 6.1 电路功能快速索引

### 6.1.1 按输入输出分类

| 输入信号 | 输出信号 | 推荐电路 | 章节位置 | 标准参数 |
|---------|---------|---------|---------|---------|
| 直流 | 正弦波 | 文氏桥振荡器 | 2.4 | R=18kΩ, C=0.1μF |
| 方波 | 三角波 | 积分器 | 2.1 | R=10kΩ, C=0.1μF |
| 三角波 | 方波 | 微分器 | 2.2 | R=10kΩ, C=0.01μF |
| 多路信号 | 求和 | 求和器 | 2.3 | R1=R2=Rf=10kΩ |
| 任意信号 | 放大 | 放大器 | 1.4 | 见增益配置表 |
| 任意信号 | 缓冲 | 跟随器 | 1.4.3 | 无需外接元件 |

### 6.1.2 按频率范围分类

| 频率范围 | 推荐电路 | 关键参数 | 注意事项 |
|---------|---------|---------|---------|
| 1Hz~10Hz | 大RC积分器 | R=100kΩ, C=1μF | 注意漂移 |
| 10Hz~1kHz | 标准配置 | R=10kΩ, C=0.1μF | **最佳范围** |
| 1kHz~10kHz | 小RC配置 | R=1kΩ, C=0.1μF | 注意带宽 |
| >10kHz | 高速运放 | 考虑换TL074 | 超出LM324范围 |

### 6.1.3 按增益要求分类

| 目标增益 | 反相配置 | 同相配置 | 应用场合 |
|---------|---------|---------|---------|
| 1倍 | R1=R2=10kΩ | 跟随器 | 缓冲隔离 |
| 2倍 | R1=10kΩ,R2=20kΩ | R1=10kΩ,R2=10kΩ | 信号放大 |
| 5倍 | R1=10kΩ,R2=50kΩ | R1=10kΩ,R2=40kΩ | 中等放大 |
| 10倍 | R1=10kΩ,R2=100kΩ | R1=10kΩ,R2=90kΩ | 大信号放大 |

---

## 6.2 故障排除快速索引

### 6.2.1 按故障现象分类

| 故障现象 | 可能原因 | 检查方法 | 解决方案 | 章节参考 |
|---------|---------|---------|---------|---------|
| 无输出 | 电源未连接 | 万用表测电源 | 重新连接电源 | 4.4.1 |
| 输出饱和 | 增益过大 | 检查反馈电阻 | 调整电阻比值 | 4.4.2 |
| 波形失真 | 频率超限 | 示波器观察 | 降低频率或换运放 | 4.4.3 |
| 电路振荡 | 缺少去耦 | 观察电源纹波 | 添加0.1μF电容 | 4.4.1 |
| 增益不准 | 电阻值错误 | 万用表测电阻 | 更换正确阻值 | 4.4.2 |
| 频率偏差 | RC参数不准 | 测量实际RC值 | 调整元件参数 | 4.4.3 |

### 6.2.2 按电路类型分类

**积分器常见问题：**
- 输出漂移 → 检查电容漏电 → 更换电容
- 积分速度慢 → RC太大 → 减小R或C
- 无法复位 → 复位开关失效 → 检查开关连接

**振荡器常见问题：**
- 不起振 → 增益不够 → 增大R4/R3比值
- 频率不准 → RC参数偏差 → 用标准值替换
- 波形失真 → 增益过大 → 减小R4/R3比值

**微分方程求解问题：**
- 初始条件错误 → 检查电容预充电 → 重新设置初值
- 系统不稳定 → 反馈回路错误 → 检查连接
- 输出幅度异常 → 缩放参数错误 → 重新计算缩放

---

## 6.3 电路搭建检查清单

### 6.3.1 搭建前准备检查

**工具材料检查：**
- [ ] 面包板（830孔）
- [ ] LM324芯片（2片）
- [ ] 跳线（红黑蓝绿黄各10根）
- [ ] 万用表、示波器
- [ ] ±5V电源

**元件准备检查：**
- [ ] 电阻：1kΩ, 10kΩ, 22kΩ, 47kΩ, 100kΩ各5个
- [ ] 电容：0.01μF, 0.1μF, 1μF各3个
- [ ] 去耦电容：0.1μF陶瓷电容10个

**设计参数检查：**
- [ ] 电路图绘制完整
- [ ] 元件参数计算正确
- [ ] 预期性能指标明确

### 6.3.2 搭建过程检查

**电源连接检查：**
- [ ] +5V连接到4号引脚
- [ ] -5V连接到11号引脚
- [ ] 每个芯片添加去耦电容
- [ ] 电源极性正确无误

**信号连接检查：**
- [ ] 输入电阻连接正确
- [ ] 反馈电阻连接正确
- [ ] 同相端接地（反相放大器）
- [ ] 输出端引出测试点

**布线质量检查：**
- [ ] 连线牢固可靠
- [ ] 无明显短路现象
- [ ] 布线整齐美观
- [ ] 测试点标识清楚

### 6.3.3 上电前检查

**安全检查：**
- [ ] 电源电压设置正确（±5V）
- [ ] 电源限流设置合理（<100mA）
- [ ] 万用表检查无短路
- [ ] 目视检查连线正确

**功能检查：**
- [ ] 电路连接与设计图一致
- [ ] 元件参数与计算值一致
- [ ] 测试设备连接就绪
- [ ] 信号源设置合理

---

## 6.4 参数计算检查清单

### 6.4.1 频率计算检查

**文氏桥振荡器：**
- [ ] 使用公式：f = 1/(2πRC)
- [ ] R1 = R2, C1 = C2
- [ ] 增益条件：Av = 3
- [ ] 频率误差<10%

**积分器时间常数：**
- [ ] 使用公式：τ = RC
- [ ] 选择合适的时间范围
- [ ] 考虑元件误差影响
- [ ] 验证线性工作区

### 6.4.2 增益计算检查

**反相放大器：**
- [ ] 使用公式：Av = -R2/R1
- [ ] 电阻比值计算正确
- [ ] 考虑5%电阻误差
- [ ] 验证不饱和条件

**同相放大器：**
- [ ] 使用公式：Av = 1 + R2/R1
- [ ] 增益恒大于1
- [ ] 电阻比值合理
- [ ] 输入阻抗足够高

### 6.4.3 功率和电流检查

**功率计算：**
- [ ] P = V²/R < 额定功率
- [ ] 选择合适功率等级
- [ ] 考虑安全裕量
- [ ] 检查发热情况

**电流限制：**
- [ ] 输出电流 < ±20mA（LM324）
- [ ] 输入偏置电流可忽略
- [ ] 电源电流 < 限流设置
- [ ] 无过流保护动作

---

## 6.5 测试验证检查清单

### 6.5.1 静态测试检查

**直流工作点：**
- [ ] 电源电压：+5V±0.1V, -5V±0.1V
- [ ] 输出静态电压合理
- [ ] 输入端电压符合虚短条件
- [ ] 无异常发热现象

**电阻测量：**
- [ ] 关键电阻值准确
- [ ] 连接电阻无虚焊
- [ ] 并联电阻计算正确
- [ ] 温度系数影响可忽略

### 6.5.2 动态测试检查

**波形质量：**
- [ ] 输出波形完整无失真
- [ ] 频率测量准确
- [ ] 幅度测量正确
- [ ] 相位关系符合预期

**频率响应：**
- [ ] 低频响应平坦
- [ ] 高频滚降正常
- [ ] 带宽满足要求
- [ ] 无寄生振荡

### 6.5.3 性能验证检查

**精度验证：**
- [ ] 增益误差<10%
- [ ] 频率误差<5%
- [ ] 相位误差<5°
- [ ] 线性度满足要求

**稳定性验证：**
- [ ] 长时间工作稳定
- [ ] 温度变化影响小
- [ ] 负载变化适应性好
- [ ] 电源变化抑制比高

---

## 6.6 5分钟速查版

### 6.6.1 关键电路参数速查

**万能配置（适用80%场合）：**
```
通用放大器：R1=10kΩ, R2=100kΩ, Av=-10
通用积分器：R=10kΩ, C=0.1μF, τ=1ms
通用振荡器：R=18kΩ, C=0.1μF, f≈88Hz
去耦电容：每个运放0.1μF陶瓷电容
```

**竞赛专用配置：**
```
600rad/s振荡器：R=16kΩ, C=0.1μF
微分方程积分器：R=10kΩ, C=0.1μF
初始条件：u₀(0)=1V, du₀/dt(0)=0
缩放参数：时间×1000, 幅度×1000
```

### 6.6.2 故障快速诊断

**30秒诊断法：**
1. **10秒目视**：检查连线和元件
2. **10秒电源**：万用表测±5V
3. **10秒波形**：示波器看输出

**常见故障速查：**
- 无输出 → 电源问题
- 输出异常 → 反馈问题
- 增益错误 → 电阻问题
- 频率偏差 → RC参数问题
- 波形失真 → 频率超限
- 电路振荡 → 去耦问题

### 6.6.3 应急替换方案

**电阻应急替换：**
- 10kΩ缺货 → 用9.1kΩ或11kΩ
- 100kΩ缺货 → 用91kΩ或110kΩ
- 精度要求不高可用±20%误差

**电容应急替换：**
- 0.1μF缺货 → 用0.082μF或0.12μF
- 陶瓷电容缺货 → 可用薄膜电容
- 去耦电容可并联小容量

**运放应急替换：**
- LM324缺货 → 用LM358（双运放）
- 高频要求 → 用TL074
- 高精度要求 → 用OP07

---

## 6.7 常见错误和解决方案对照表

### 6.7.1 设计阶段错误

| 错误类型 | 具体表现 | 根本原因 | 正确做法 | 预防措施 |
|---------|---------|---------|---------|---------|
| 参数计算错误 | 频率偏差>20% | 公式使用错误 | 重新计算验证 | 使用标准公式 |
| 增益设计错误 | 输出饱和削波 | 未考虑电源限制 | 降低增益设计 | 留有裕量 |
| 频率选择错误 | 超出运放带宽 | 忽略GBW限制 | 选择合适频率 | 检查带宽积 |
| 功率估算错误 | 电阻过热 | 功率计算不准 | 重新计算功率 | 选择大功率 |

### 6.7.2 搭建阶段错误

| 错误类型 | 具体表现 | 根本原因 | 正确做法 | 预防措施 |
|---------|---------|---------|---------|---------|
| 电源极性错误 | 运放损坏 | 粗心大意 | 仔细核对极性 | 标记电源线 |
| 引脚接错 | 功能异常 | 引脚图看错 | 对照引脚图 | 使用标准图 |
| 虚焊断线 | 时好时坏 | 焊接质量差 | 重新焊接 | 检查连接 |
| 短路现象 | 电源过流 | 布线错误 | 仔细检查布线 | 分步搭建 |

### 6.7.3 调试阶段错误

| 错误类型 | 具体表现 | 根本原因 | 正确做法 | 预防措施 |
|---------|---------|---------|---------|---------|
| 测量方法错误 | 数据不准确 | 仪器使用不当 | 学习正确使用 | 校准仪器 |
| 负载效应 | 接入探头后异常 | 探头阻抗影响 | 使用高阻探头 | 选择合适探头 |
| 接地问题 | 波形有噪声 | 接地回路干扰 | 改善接地 | 单点接地 |
| 干扰问题 | 信号不稳定 | 外界干扰 | 屏蔽处理 | 远离干扰源 |

### 6.7.4 系统集成错误

| 错误类型 | 具体表现 | 根本原因 | 正确做法 | 预防措施 |
|---------|---------|---------|---------|---------|
| 阻抗匹配错误 | 信号衰减 | 前后级不匹配 | 添加缓冲器 | 计算阻抗 |
| 相位错误 | 反馈不稳定 | 相位关系错误 | 检查相位 | 使用示波器 |
| 增益分配错误 | 某级饱和 | 增益分配不当 | 重新分配增益 | 均匀分配 |
| 频率响应错误 | 带宽不够 | 级联效应 | 考虑级联影响 | 留有余量 |

---

## 6.8 考场应急处理指南

### 6.8.1 时间管理策略

**时间分配建议：**
- 理论分析：30分钟
- 电路设计：60分钟
- 搭建调试：90分钟
- 测试验证：60分钟
- 报告整理：30分钟

**关键时间节点：**
- 2小时内：完成基本电路搭建
- 3小时内：实现基本功能
- 4小时内：完成性能测试
- 最后1小时：优化和报告

### 6.8.2 优先级处理

**功能优先级：**
1. **最高优先级**：基本功能实现
2. **高优先级**：性能指标达标
3. **中优先级**：稳定性优化
4. **低优先级**：外观和报告

**问题处理优先级：**
1. **致命问题**：无输出、损坏器件
2. **严重问题**：功能异常、性能差
3. **一般问题**：精度不够、稳定性差
4. **轻微问题**：外观、文档问题

### 6.8.3 应急备案

**器件损坏应急：**
- 运放损坏 → 使用备用芯片
- 电阻烧毁 → 串并联组合
- 电容失效 → 并联小电容
- 面包板损坏 → 转移到新板

**功能异常应急：**
- 振荡器不起振 → 降低频率重试
- 积分器漂移 → 添加复位电路
- 放大器饱和 → 降低增益
- 系统不稳定 → 简化设计

**时间不够应急：**
- 简化非关键功能
- 使用标准参数配置
- 跳过优化步骤
- 重点保证基本功能

---

## 📖 本章小结

**快速索引体系：**
1. **功能索引**：按输入输出、频率、增益分类查找
2. **故障索引**：按现象、电路类型快速定位问题
3. **参数索引**：标准配置和应急替换方案
4. **检查索引**：全流程检查清单和验证方法

**检查清单体系：**
1. **五级检查**：准备→搭建→上电→测试→验证
2. **三类检查**：静态→动态→性能全覆盖
3. **应急处理**：时间管理、优先级、备案方案
4. **错误预防**：常见错误和解决方案对照

**考场实用特色：**
1. **5分钟速查**：关键信息快速提取
2. **30秒诊断**：故障快速定位方法
3. **应急替换**：元件缺货的备选方案
4. **时间管理**：考场环境下的策略指导

**下一章预告：**
第七章将进行教学文档的整合和格式优化，制作完整版参考资料。
