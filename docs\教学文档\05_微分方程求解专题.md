# 第五章：微分方程求解专题

## 📚 学习目标
- 理解模拟计算机求解微分方程的基本原理
- 掌握状态变量法的应用和优势
- 学会设计二阶微分方程的模拟计算机
- 能够处理不同输入信号和初始条件

---

## 5.1 2023年竞赛题分析

### 5.1.1 题目微分方程
**原始方程：**
```
d²u₀(t)/dt² + 4×10⁴ du₀(t)/dt = 3×10⁶ - 4u₁(t)
```

**标准形式：**
```
d²u₀/dt² + 40000 du₀/dt = 3000000 - 4u₁(t)
```

**初始条件：**
- u₀(0) = 1V
- du₀(0)/dt = 0

**输入信号u₁(t)：**
1. u₁(t) = 0（齐次方程）
2. u₁(t) = 正弦波（ω=600rad/s，幅度>2V）
3. u₁(t) = 三角波（与正弦波同频同相，幅度>2V）

### 5.1.2 方程特征分析

**系数分析：**
- 二阶项系数：1
- 一阶项系数：4×10⁴ = 40,000
- 常数项：3×10⁶ = 3,000,000
- 输入项系数：-4

**时间尺度分析：**
- 特征方程：s² + 40000s = 0
- 特征根：s₁ = 0, s₂ = -40000
- 时间常数：τ = 1/40000 = 25μs

**频率特性：**
- 主导极点：s = -40000 rad/s
- 对应频率：f = 40000/(2π) ≈ 6366Hz
- 输入频率：600 rad/s ≈ 95.5Hz（远小于主导频率）

### 5.1.3 理论解预览

**齐次解（u₁=0）：**
```
u₀(t) = A + B·e^(-40000t)
```
应用初始条件：u₀(0)=1, du₀(0)/dt=0
得到：u₀(t) = 75 - 74·e^(-40000t)

**非齐次解（u₁≠0）：**
包含齐次解 + 特解，特解形式取决于输入信号类型。

---

## 5.2 模拟计算机基本原理

### 5.2.1 基本思想

**核心概念：**
模拟计算机通过运放电路实现数学运算，将微分方程转换为电路网络求解。

**基本运算单元：**
- **积分器**：实现 ∫f(t)dt
- **求和器**：实现 a·f₁(t) + b·f₂(t)
- **比例器**：实现 k·f(t)
- **反相器**：实现 -f(t)

**求解策略：**
1. 将最高阶导数表示为其他项的组合
2. 通过积分器逐级降阶
3. 形成反馈系统求解

### 5.2.2 直接法实现

**方程重排：**
```
d²u₀/dt² = 3×10⁶ - 4u₁(t) - 4×10⁴ du₀/dt
```

**系统框图：**
```
3×10⁶ ----+
           |
-4u₁(t) ---+---> [求和器] ---> d²u₀/dt² ---> [积分器1] ---> du₀/dt ---> [积分器2] ---> u₀
           |                                                      |
-4×10⁴ ----+                                                      |
    ^                                                              |
    |______________________________________________________________|
                            反馈回路
```

**电路实现挑战：**
- 系数很大（4×10⁴, 3×10⁶）
- 需要精确的比例放大器
- 直流偏置问题

### 5.2.3 状态变量法（推荐）

**状态变量定义：**
```
x₁ = u₀
x₂ = du₀/dt
```

**状态方程组：**
```
dx₁/dt = x₂
dx₂/dt = 3×10⁶ - 4u₁(t) - 4×10⁴x₂
```

**优势分析：**
1. 只需要积分器，不需要微分器
2. 系统更稳定，噪声小
3. 初始条件设置简单
4. 便于系统分析和调试

---

## 5.3 状态变量法详细设计

### 5.3.1 系统框图

**完整系统：**
```
u₁(t) ---> [-4] ---> [求和器] ---> dx₂/dt ---> [积分器2] ---> x₂ ---> [积分器1] ---> x₁=u₀
             ^                                                   |                      |
             |                                                   |                      |
        3×10⁶ +                                                  |                      |
             ^                                                   |                      |
             |                                                   v                      |
        [-4×10⁴] <-------------------------------------------- x₂                      |
             ^                                                                          |
             |                                                                          |
             +--------------------------------------------------------------------------+
                                        反馈回路
```

### 5.3.2 参数缩放设计

**问题：** 系数4×10⁴和3×10⁶太大，难以用运放电路实现

**解决方案：** 时间和幅度缩放

**时间缩放：**
设 τ = t/T，其中T为时间缩放因子
选择 T = 1/1000，则 τ = 1000t

**缩放后方程：**
```
d²u₀/dτ² + 40 du₀/dτ = 3000 - 4u₁(τ)
```

**幅度缩放：**
设 v₀ = u₀/V，其中V为幅度缩放因子
选择 V = 1000，则 v₀ = u₀/1000

**最终缩放方程：**
```
d²v₀/dτ² + 40 dv₀/dτ = 3 - 0.004u₁(τ)
```

### 5.3.3 实用电路参数

**积分器设计：**
- 积分器1：R₁ = 10kΩ, C₁ = 0.1μF，τ₁ = RC = 1ms
- 积分器2：R₂ = 10kΩ, C₂ = 0.1μF，τ₂ = RC = 1ms

**求和器设计：**
- 常数项输入：3V直流，通过10kΩ电阻
- u₁(t)输入：通过250kΩ电阻（实现0.004系数）
- 反馈项：通过250Ω电阻（实现40倍系数）

**比例关系：**
```
实际时间 = 仿真时间 × 1000
实际电压 = 仿真电压 × 1000
```

---

## 5.4 电路实现方案

### 5.4.1 完整电路图

**运放分配：**
- U1A：积分器1（x₂ → x₁）
- U1B：积分器2（dx₂/dt → x₂）
- U1C：求和器
- U1D：反相器（如需要）

**积分器1电路：**
```
x₂ ----[10kΩ]----+----[-]
                  |       \
                  |        U1A -----> x₁
                 [0.1μF]  /
                  |      [+]
                 GND      |
                         GND
```

**积分器2电路：**
```
求和输出 ----[10kΩ]----+----[-]
                        |       \
                        |        U1B -----> x₂
                       [0.1μF]  /
                        |      [+]
                       GND      |
                              GND
```

**求和器电路：**
```
+3V ----[10kΩ]----+
                   |
u₁(t) --[250kΩ]---+----[-]
                   |       \
x₂ -----[250Ω]----+        U1C -----> 求和输出
                   |       /
                  [10kΩ]  [+]
                   |       |
                  输出    GND
```

### 5.4.2 初始条件设置

**积分器初始条件：**
- x₁(0) = u₀(0) = 1V
- x₂(0) = du₀(0)/dt = 0V

**设置方法1：电容预充电**
```
    +1V
     |
    [S1] ← 开关，t=0时断开
     |
x₂--[R]--+----[-]
          |       \
         [C]       U1A -----> x₁
          |       /
         GND     [+]
                  |
                 GND
```

**设置方法2：复位开关**
```
x₂--[R]--+----[-]
          |       \
         [C]       U1A -----> x₁
          |       /
         [S2]    [+] ← S2为复位开关
          |       |
         GND     GND
```

### 5.4.3 输入信号处理

**信号源要求：**
1. **齐次解测试**：u₁(t) = 0V
2. **正弦波输入**：u₁(t) = 2sin(600t) V
3. **三角波输入**：u₁(t) = 2V三角波，f = 95.5Hz

**信号调理：**
- 幅度调整：确保输入幅度>2V
- 频率校准：使用频率计验证600rad/s
- 相位对齐：三角波与正弦波同相

**输入保护：**
```
信号源 ----[1kΩ]----+----[运放输入]
                     |
                   [钳位二极管]
                     |
                    GND
```

---

## 5.5 系统连接和调试步骤

### 5.5.1 分步搭建流程

**第一步：搭建积分器（10分钟）**
1. 搭建积分器1（x₂ → x₁）
2. 搭建积分器2（求和输出 → x₂）
3. 单独测试每个积分器功能
4. 验证时间常数τ = RC = 1ms

**第二步：搭建求和器（10分钟）**
1. 连接常数项输入（+3V）
2. 连接u₁(t)输入端
3. 连接反馈输入端
4. 测试求和器各输入权重

**第三步：系统连接（5分钟）**
1. 连接积分器之间的信号线
2. 连接反馈回路
3. 设置初始条件电路
4. 添加测试点和示波器探头

**第四步：系统调试（15分钟）**
1. 静态测试：检查直流工作点
2. 动态测试：输入测试信号
3. 参数调整：微调电阻值
4. 性能验证：测量频率响应

### 5.5.2 关键测试点

**必测点位：**
1. **TP1**：积分器1输出（x₁ = u₀）
2. **TP2**：积分器2输出（x₂ = du₀/dt）
3. **TP3**：求和器输出（d²u₀/dt²）
4. **TP4**：输入信号u₁(t)

**测试点布置：**
```
u₁(t) -----> [TP4] -----> [求和器] -----> [TP3] -----> [积分器2] -----> [TP2] -----> [积分器1] -----> [TP1] = u₀
                              ^                                              |
                              |                                              |
                              +----------------------------------------------+
                                            反馈回路
```

### 5.5.3 调试检查清单

**静态检查：**
- [ ] 电源电压正确（±5V）
- [ ] 去耦电容已添加
- [ ] 初始条件设置正确
- [ ] 各测试点直流电压合理

**动态检查：**
- [ ] 积分器功能正常（方波→三角波）
- [ ] 求和器权重正确
- [ ] 反馈回路连接无误
- [ ] 系统无自激振荡

**性能检查：**
- [ ] 输出波形符合预期
- [ ] 频率响应正确
- [ ] 幅度关系准确
- [ ] 相位关系正确

---

## 5.6 三种输入信号测试

### 5.6.1 齐次方程解测试（u₁=0）

**测试设置：**
- 输入：u₁(t) = 0V
- 初始条件：u₀(0) = 1V, du₀(0)/dt = 0
- 预期输出：指数衰减曲线

**理论解：**
```
u₀(t) = 75 - 74·e^(-40000t)
```

**实测要点：**
1. 观察输出是否从1V开始
2. 检查是否单调上升趋向75V
3. 测量时间常数（约25μs）
4. 验证最终稳态值

**常见问题：**
- 输出不从1V开始 → 检查初始条件设置
- 输出振荡 → 检查反馈回路稳定性
- 时间常数不对 → 检查积分器参数

### 5.6.2 正弦波输入测试

**测试设置：**
- 输入：u₁(t) = 2sin(600t) V
- 频率：f = 600/(2π) ≈ 95.5Hz
- 幅度：>2V

**理论解：**
```
u₀(t) = 齐次解 + 特解
特解形式：A·sin(600t + φ)
```

**测试步骤：**
1. 设置函数发生器输出95.5Hz正弦波
2. 调整幅度到2V以上
3. 连接到系统输入端
4. 观察输出波形

**测量参数：**
- 输出频率（应为95.5Hz）
- 输出幅度
- 相位差
- 稳态时间

### 5.6.3 三角波输入测试

**测试设置：**
- 输入：u₁(t) = 三角波，f = 95.5Hz，幅度>2V
- 相位：与正弦波同相
- 波形：对称三角波

**三角波生成：**
方法1：函数发生器直接输出
方法2：正弦波→比较器→积分器

**同相验证：**
1. 同时显示正弦波和三角波
2. 检查过零点是否同时
3. 验证峰值点对应关系

**测试要点：**
- 三角波线性度
- 频率准确性
- 相位关系
- 幅度匹配

---

## 5.7 理论解与实测对比

### 5.7.1 齐次解对比

**理论计算：**
```
特征方程：s² + 40000s = 0
特征根：s₁ = 0, s₂ = -40000
通解：u₀(t) = C₁ + C₂·e^(-40000t)
应用初始条件：u₀(0) = 1, du₀(0)/dt = 0
得：C₁ = 75, C₂ = -74
最终解：u₀(t) = 75 - 74·e^(-40000t)
```

**关键参数：**
- 初始值：u₀(0) = 1V
- 稳态值：u₀(∞) = 75V
- 时间常数：τ = 1/40000 = 25μs
- 63%响应时间：25μs

**实测验证：**
1. 测量t=0时的输出值
2. 测量稳态输出值
3. 测量63%响应时间
4. 计算实际时间常数

### 5.7.2 正弦波响应对比

**频域分析：**
```
系统传递函数：H(s) = 1/(s² + 40000s)
在s = j600处：
|H(j600)| = 1/√(600² + (40000×600)²) ≈ 4.17×10⁻⁸
∠H(j600) = -arctan(40000×600/600²) ≈ -90°
```

**理论预测：**
- 输出幅度极小（几乎为0）
- 相位滞后约90°
- 主要为稳态响应

**实测对比：**
1. 测量输出幅度
2. 测量相位差
3. 观察瞬态过程
4. 验证稳态响应

### 5.7.3 误差分析

**主要误差源：**
1. **元件误差**：电阻电容精度（±5%~±20%）
2. **运放非理想性**：偏置电流、失调电压
3. **寄生参数**：布线电容、电阻
4. **测量误差**：示波器精度、探头负载

**误差估算：**
- 时间常数误差：±10%（主要由RC精度决定）
- 幅度误差：±5%（主要由电阻比值决定）
- 频率误差：±2%（主要由积分器精度决定）

**改善方法：**
1. 使用精密电阻电容（1%精度）
2. 选择高性能运放
3. 优化电路布局
4. 校准测量仪器

---

## 5.8 实用设计技巧

### 5.8.1 参数选择策略

**时间缩放选择：**
- 原则：使缩放后的时间常数在1ms~10ms范围
- 计算：T = 原时间常数 × 1000
- 验证：确保积分器工作在线性区

**幅度缩放选择：**
- 原则：输出电压在±5V范围内
- 计算：V = 预期最大输出 / 4V
- 验证：确保运放不饱和

**电阻选择：**
- 积分电阻：10kΩ（标准值）
- 求和电阻：根据权重计算
- 反馈电阻：根据增益要求

### 5.8.2 稳定性设计

**避免振荡：**
1. 合理选择积分器参数
2. 添加适当的阻尼
3. 检查反馈回路增益
4. 优化电路布局

**提高精度：**
1. 使用精密元件
2. 温度补偿设计
3. 偏置电流补偿
4. 定期校准

### 5.8.3 调试技巧

**快速定位故障：**
1. 分模块测试
2. 信号追踪法
3. 对比理论值
4. 参数扫描

**性能优化：**
1. 参数微调
2. 负载匹配
3. 噪声抑制
4. 温度稳定

---

## 📖 本章小结

**核心方法：**
1. **状态变量法**：将高阶微分方程转换为一阶方程组
2. **参数缩放**：时间和幅度缩放，适应电路实现
3. **模块化设计**：积分器+求和器的标准组合
4. **系统调试**：分步搭建，逐级验证

**2023竞赛题要点：**
1. **方程特征**：二阶系统，大系数，需要缩放
2. **三种输入**：齐次、正弦波、三角波测试
3. **初始条件**：u₀(0)=1V, du₀(0)/dt=0
4. **理论对比**：验证实测结果的正确性

**实用技巧：**
1. **参数选择**：合理缩放，标准元件值
2. **电路实现**：模块化搭建，系统化调试
3. **误差控制**：精密元件，校准测量
4. **故障排除**：分模块测试，信号追踪

**下一章预告：**
第六章将制作快速索引和检查清单，便于考场快速查找和问题诊断。
