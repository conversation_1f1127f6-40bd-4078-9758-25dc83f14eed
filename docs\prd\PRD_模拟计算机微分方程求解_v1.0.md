# 模拟计算机微分方程求解系统 PRD v1.0

## 1. 文档信息
- **版本**: v1.0
- **创建时间**: 2024-12-19
- **负责人**: Emma (产品经理)
- **项目类型**: 电子设计竞赛综合测评

## 2. 背景与问题陈述
### 核心问题
需要设计一个基于运算放大器的模拟计算机，求解二阶线性常微分方程：
```
d²u₀(t)/dt² + 4×10⁴ du₀(t)/dt = 3×10⁶ - 4u₁(t)
```

### 技术约束
- 仅能使用2片LM324DR四运算放大器芯片
- 电阻、电容数量不限，参数不限
- ±5V直流电源
- 禁止增加IC芯片

## 3. 目标与成功指标
### 主要目标
1. **信号生成器**：生成正弦波、三角波（ω=600rad/s，幅度>2V）
2. **基础运算单元**：构建微分器、积分器模块
3. **系统集成**：完整的模拟计算机求解系统

### 评分指标
- 理论设计：3分
- 正弦波生成：3分  
- 三角波生成：4分
- 微分积分器：6分
- 齐次方程解：6分
- 非齐次解：8分（4+4）
- **总分：30分**

## 4. 功能规格详述
### 4.1 信号发生器模块
**输入**：±5V电源
**输出**：
- 正弦波：ω=600rad/s，幅度>2V
- 三角波：与正弦波同频同相，幅度>2V

### 4.2 运算单元模块
**微分器**：实现 k·du/dt 运算
**积分器**：实现 (1/k)∫u dt 运算，支持初始条件设置

### 4.3 系统求解模块
**输入信号类型**：
1. u₁(t) = 0（齐次方程）
2. u₁(t) = 正弦波（600rad/s）
3. u₁(t) = 三角波（600rad/s）

**初始条件**：
- u₀(0) = 1V
- du₀(0)/dt = 0

## 5. 技术实现策略
### 5.1 电路拓扑选择
- **文氏桥振荡器**：生成正弦波
- **积分器级联**：正弦波转三角波
- **反相积分器**：实现积分运算
- **微分器**：实现微分运算（需要噪声抑制）

### 5.2 参数设计要点
- 频率设计：f = 600/(2π) ≈ 95.5Hz
- 幅度控制：运放输出限制在±5V内
- 相位匹配：确保正弦波与三角波同相

## 6. 风险评估
### 高风险项
- **微分器噪声**：高频噪声放大问题
- **频率精度**：RC参数精度影响
- **幅度稳定性**：温度漂移影响

### 缓解策略
- 微分器增加低通滤波
- 使用精密电阻电容
- 设计幅度反馈控制

## 7. 测试验证计划
### 单元测试
- 各运算单元独立功能验证
- 频率、幅度、相位测量

### 系统测试  
- 三种输入条件下的求解结果
- 与理论解对比验证

## 8. 交付物清单
- 完整电路原理图
- 参数计算表
- 实物制作
- 测试数据记录
- 设计报告（2页手写）